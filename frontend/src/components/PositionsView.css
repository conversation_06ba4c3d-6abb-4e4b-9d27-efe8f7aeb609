.positions-view {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.positions-tabs {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 2px solid #e5e7eb;
}

.positions-tabs button {
  padding: 12px 24px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.positions-tabs button:hover {
  color: #374151;
}

.positions-tabs button.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.positions-table-container h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.table-wrapper {
  overflow-x: auto;
}

.positions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.positions-table th,
.positions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.positions-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.positions-table tbody tr:hover {
  background-color: #f9fafb;
}

.direction.buy {
  color: #22c55e;
  font-weight: 600;
}

.direction.sell {
  color: #ef4444;
  font-weight: 600;
}

.close-button {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.close-button:hover {
  background-color: #dc2626;
}

.loading {
  text-align: center;
  padding: 48px;
  color: #6b7280;
  font-size: 16px;
}

@media (max-width: 768px) {
  .positions-view {
    padding: 16px;
  }
  
  .positions-tabs {
    flex-direction: column;
  }
  
  .positions-tabs button {
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    border-right: none;
  }
  
  .positions-tabs button.active {
    border-bottom-color: #e5e7eb;
    border-left: 3px solid #007bff;
    background-color: #f8f9fa;
  }
}