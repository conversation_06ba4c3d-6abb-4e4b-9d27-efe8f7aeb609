import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import apiService from '../services/api';
import './EquityCurveChart.css';

interface EquityCurveHandle {
  loadEquityCurveData: () => Promise<void>;
}

const EquityCurveChart = forwardRef<EquityCurveHandle, {}>((_, ref) => {
  const [equityData, setEquityData] = useState<Array<[string, number]>>([]);
  const [loading, setLoading] = useState(true);
  const [PlotComponent, setPlotComponent] = useState<any>(null);
  const [plotlyError, setPlotlyError] = useState<string | null>(null);

  const loadEquityCurveData = async () => {
    try {
      const data = await apiService.getEquityCurveData();
      setEquityData(data);
    } catch (error) {
      console.error('Failed to load equity curve data:', error);
    } finally {
      setLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    loadEquityCurveData,
  }));

  useEffect(() => {
    // Load both Plotly and data
    const initializeComponent = async () => {
      try {
        setLoading(true);

        // Load Plotly dynamically
        const Plot = await import('react-plotly.js');
        setPlotComponent(() => Plot.default);

        // Load equity data
        await loadEquityCurveData();

      } catch (error) {
        console.error('Failed to initialize component:', error);
        setPlotlyError('Failed to load chart library');
      } finally {
        setLoading(false);
      }
    };

    initializeComponent();
  }, []);

  if (loading) {
    return <div className="loading">Loading equity curve...</div>;
  }

  if (plotlyError) {
    return (
      <div className="equity-curve-container">
        <h3>Equity Curve</h3>
        <div className="no-data">
          <p>Error loading chart: {plotlyError}</p>
          <button onClick={() => window.location.reload()}>Reload Page</button>
        </div>
      </div>
    );
  }

  if (!PlotComponent) {
    return <div className="loading">Loading chart library...</div>;
  }

  if (equityData.length === 0) {
    return (
      <div className="equity-curve-container">
        <h3>Equity Curve</h3>
        <div className="no-data">
          <p>No trading history available yet.</p>
          <p>Start trading to see your equity curve develop over time.</p>
        </div>
      </div>
    );
  }

  // Extract dates and cumulative P&L for plotting
  const dates = equityData.map(point => point[0]);
  const cumulativePnL = equityData.map(point => point[1]);

  return (
    <div className="equity-curve-container">
      <div className="equity-curve-header">
        <h3>Equity Curve</h3>
        <button
          className="refresh-button"
          onClick={async () => {
            setLoading(true);
            await loadEquityCurveData();
            setLoading(false);
          }}
          disabled={loading}
        >
          Refresh
        </button>
      </div>
      
      <div className="chart-container">
        <PlotComponent
          data={[
            {
              x: dates,
              y: cumulativePnL,
              type: 'scatter',
              mode: 'lines+markers',
              name: 'Cumulative P&L',
              line: {
                color: cumulativePnL[cumulativePnL.length - 1] >= 0 ? '#22c55e' : '#ef4444',
                width: 3,
              },
              marker: {
                color: '#007bff',
                size: 6,
              },
              hovertemplate: 
                '<b>Date:</b> %{x}<br>' +
                '<b>Cumulative P&L:</b> $%{y:,.2f}<br>' +
                '<extra></extra>',
            },
          ]}
          layout={{
            title: {
              text: 'Account Equity Growth Over Time',
              font: { size: 18, color: '#1f2937' },
            },
            xaxis: {
              title: 'Date',
              type: 'date',
              gridcolor: '#e5e7eb',
              tickangle: -45,
            },
            yaxis: {
              title: 'Cumulative P&L (USD)',
              gridcolor: '#e5e7eb',
              tickformat: '$,.0f',
              zeroline: true,
              zerolinecolor: '#6b7280',
              zerolinewidth: 2,
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'white',
            margin: { l: 80, r: 40, t: 60, b: 80 },
            hovermode: 'x unified',
            showlegend: false,
          }}
          config={{
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
            displaylogo: false,
          }}
          style={{ width: '100%', height: '500px' }}
        />
      </div>
      
      <div className="equity-stats">
        <div className="stat-card">
          <h4>Total Trades</h4>
          <p>{equityData.length}</p>
        </div>
        <div className="stat-card">
          <h4>Current P&L</h4>
          <p 
            style={{ 
              color: cumulativePnL[cumulativePnL.length - 1] >= 0 ? '#22c55e' : '#ef4444' 
            }}
          >
            ${cumulativePnL[cumulativePnL.length - 1]?.toFixed(2) || '0.00'}
          </p>
        </div>
        <div className="stat-card">
          <h4>Best Day</h4>
          <p style={{ color: '#22c55e' }}>
            ${Math.max(...cumulativePnL).toFixed(2)}
          </p>
        </div>
        <div className="stat-card">
          <h4>Worst Day</h4>
          <p style={{ color: '#ef4444' }}>
            ${Math.min(...cumulativePnL).toFixed(2)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EquityCurveChart;