import React from 'react';
import './Dashboard.css';

const DashboardSimple: React.FC = () => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const getPnLColor = (value: number) => {
    if (value > 0) return '#22c55e'; // green
    if (value < 0) return '#ef4444'; // red
    return '#6b7280'; // gray
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>Performance Summary</h2>
        <button className="refresh-button">
          Refresh Prices
        </button>
      </div>
      
      <div className="kpi-grid">
        <div className="kpi-card">
          <h3>Total Realized P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(0) }}
          >
            {formatCurrency(0)}
          </p>
        </div>
        
        <div className="kpi-card">
          <h3>Total Unrealized P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(0) }}
          >
            {formatCurrency(0)}
          </p>
        </div>
        
        <div className="kpi-card">
          <h3>Total P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(0) }}
          >
            {formatCurrency(0)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardSimple;