.equity-curve-container {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.equity-curve-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.equity-curve-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.refresh-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.refresh-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.refresh-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.chart-container {
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.no-data {
  text-align: center;
  padding: 48px;
  color: #6b7280;
}

.no-data p {
  margin: 8px 0;
  font-size: 16px;
}

.equity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.stat-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.stat-card h4 {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card p {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}

.loading {
  text-align: center;
  padding: 48px;
  color: #6b7280;
  font-size: 16px;
}

@media (max-width: 768px) {
  .equity-curve-container {
    padding: 16px;
  }
  
  .equity-curve-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .refresh-button {
    width: 100%;
  }
  
  .equity-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}