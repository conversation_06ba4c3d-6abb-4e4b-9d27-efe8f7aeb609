import React, { useState, useRef } from 'react';
import DashboardWorking from './DashboardWorking';
import CreatePositionWorking from './CreatePositionWorking';
import PositionsWorking from './PositionsWorking';
import EquityCurveChart from "./EquityCurveChart";
import './TradingSimulator.css';
// Force recompile

const TradingSimulator: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'positions' | 'equity-curve' | 'create-position'>('positions');
  const positionsRef = useRef<{ loadData: () => void }>(null);
  const equityCurveRef = useRef<{ loadEquityCurveData: () => void }>(null);

  const handleGlobalRefresh = () => {
    positionsRef.current?.loadData();
    equityCurveRef.current?.loadEquityCurveData();
  };

  return (
    <div className="trading-simulator">
      <DashboardWorking />
      
      <div className="tab-navigation">
        <button 
          className={activeTab === 'positions' ? 'active' : ''}
          onClick={() => setActiveTab('positions')}
        >
          Positions
        </button>
        <button 
          className={activeTab === 'equity-curve' ? 'active' : ''}
          onClick={() => setActiveTab('equity-curve')}
        >
          Equity Curve
        </button>
        <button 
          className={activeTab === 'create-position' ? 'active' : ''}
          onClick={() => setActiveTab('create-position')}
        >
          Create Position
        </button>
      </div>

      <div className="tab-content" style={{ padding: '20px' }}>
        {activeTab === 'positions' && <PositionsWorking ref={positionsRef} />}
        {activeTab === 'equity-curve' && <EquityCurveChart ref={equityCurveRef} />}
        {activeTab === 'create-position' && <CreatePositionWorking onPositionCreated={handleGlobalRefresh} />}
      </div>
    </div>
  );
};

export default TradingSimulator;