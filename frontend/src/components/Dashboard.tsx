import React, { useState, useEffect } from 'react';
import apiService from '../services/api';
import { PerformanceSummary } from '../types';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const [performance, setPerformance] = useState<PerformanceSummary>({
    total_realized_pnl: 0,
    total_unrealized_pnl: 0,
    total_pnl: 0,
  });
  const [loading, setLoading] = useState(false);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      const data = await apiService.getPerformanceSummary();
      setPerformance(data);
    } catch (error) {
      console.error('Failed to load performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshPrices = async () => {
    try {
      setLoading(true);
      await apiService.refreshPrices();
      await loadPerformanceData();
      alert('Prices refreshed successfully!');
    } catch (error) {
      console.error('Failed to refresh prices:', error);
      alert('Failed to refresh prices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const getPnLColor = (value: number) => {
    if (value > 0) return '#22c55e'; // green
    if (value < 0) return '#ef4444'; // red
    return '#6b7280'; // gray
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>Performance Summary</h2>
        <button 
          className="refresh-button"
          onClick={handleRefreshPrices}
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh Prices'}
        </button>
      </div>
      
      <div className="kpi-grid">
        <div className="kpi-card">
          <h3>Total Realized P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(performance.total_realized_pnl) }}
          >
            {formatCurrency(performance.total_realized_pnl)}
          </p>
        </div>
        
        <div className="kpi-card">
          <h3>Total Unrealized P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(performance.total_unrealized_pnl) }}
          >
            {formatCurrency(performance.total_unrealized_pnl)}
          </p>
        </div>
        
        <div className="kpi-card">
          <h3>Total P&L</h3>
          <p 
            className="kpi-value"
            style={{ color: getPnLColor(performance.total_pnl) }}
          >
            {formatCurrency(performance.total_pnl)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;