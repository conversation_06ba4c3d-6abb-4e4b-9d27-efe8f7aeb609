const API_BASE_URL = 'http://localhost:8000';

interface PerformanceSummary {
  total_realized_pnl: number;
  total_unrealized_pnl: number;
  total_pnl: number;
}

class ApiServiceSimple {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getPerformanceSummary(): Promise<PerformanceSummary> {
    return this.request('/api/dashboard/performance');
  }

  async refreshPrices(): Promise<{ message: string }> {
    return this.request('/api/positions/refresh-prices', {
      method: 'POST',
    });
  }
}

export default new ApiServiceSimple();