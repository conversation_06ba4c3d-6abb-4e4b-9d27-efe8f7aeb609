# Trading Simulator

A Python/React web-based trading simulator for FX instruments with real-time tracking of realized and unrealized P&L.

## Backend Setup

1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the FastAPI server:
   ```bash
   python app/main.py
   ```

## Configuration

Edit `backend/config.ini` to modify:
- HOST_URL: Server host (default: localhost)
- PORT: Server port (default: 8000)
- BASE_CURRENCY: Base currency for P&L calculation (default: USD)
- DATABASE_PATH: DuckDB database file location
- PRICE_UPDATE_INTERVAL_SECONDS: Price update frequency

## API Endpoints

- `GET /` - API status
- `GET /health` - Health check

## Project Structure

```
backend/
├── app/                 # FastAPI application
├── config/             # Configuration management
├── models/             # Data models
├── services/           # Business logic services
├── api/                # API endpoints
└── data/               # Database files

frontend/
├── src/
│   ├── components/     # React components
│   ├── services/       # API services
│   └── utils/          # Utilities
└── public/             # Static assets
```