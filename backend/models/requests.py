from pydantic import BaseModel
from .enums import Direction, AssetClass

from datetime import datetime

class CreatePositionRequest(BaseModel):
    """Request model for creating a new position."""
    asset_class: AssetClass
    symbol: str
    direction: Direction
    size: float
    entry_time: datetime
    
    class Config:
        use_enum_values = True

class ClosePositionRequest(BaseModel):
    """Request model for closing a position."""
    position_id: str