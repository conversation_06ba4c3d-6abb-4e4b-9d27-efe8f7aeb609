from datetime import datetime
from typing import Optional, Union
from pydantic import BaseModel
from .enums import Direction, AssetClass

class Trade(BaseModel):
    """Represents a completed trade."""
    
    id: Optional[str] = None
    asset_class: Union[AssetClass, str]
    symbol: str
    direction: Union[Direction, str]
    size: float
    entry_time: datetime
    entry_price: float
    close_time: datetime
    close_price: float
    realized_pnl_local: float
    realized_pnl_base: float
    
    class Config:
        use_enum_values = True
    
    @classmethod
    def from_position(cls, position: 'Position', close_time: datetime, 
                     close_price: float, fx_rate: float = 1.0) -> 'Trade':
        """Create a Trade from a Position when closing."""
        from .position import Position
        
        # Calculate realized PnL
        position_direction = position.direction.value if hasattr(position.direction, 'value') else position.direction
        if position_direction == "buy":
            pnl_per_unit = close_price - position.entry_price
        else:  # SELL
            pnl_per_unit = position.entry_price - close_price
        
        realized_pnl_local = pnl_per_unit * position.size
        realized_pnl_base = realized_pnl_local * fx_rate
        
        return cls(
            asset_class=position.asset_class,
            symbol=position.symbol,
            direction=position.direction,
            size=position.size,
            entry_time=position.entry_time,
            entry_price=position.entry_price,
            close_time=close_time,
            close_price=close_price,
            realized_pnl_local=realized_pnl_local,
            realized_pnl_base=realized_pnl_base
        )
    
    def to_dict(self) -> dict:
        """Convert trade to dictionary for API responses."""
        asset_class_val = self.asset_class.value if hasattr(self.asset_class, 'value') else self.asset_class
        direction_val = self.direction.value if hasattr(self.direction, 'value') else self.direction
        
        return {
            "id": self.id,
            "asset_class": asset_class_val,
            "symbol": self.symbol,
            "direction": direction_val,
            "size": self.size,
            "entry_time": self.entry_time.isoformat(),
            "entry_price": self.entry_price,
            "close_time": self.close_time.isoformat(),
            "close_price": self.close_price,
            "realized_pnl_local": round(self.realized_pnl_local, 2),
            "realized_pnl_base": round(self.realized_pnl_base, 2)
        }