from datetime import datetime
from typing import Optional, Union
from pydantic import BaseModel, field_validator
from .enums import Direction, AssetClass

class Position(BaseModel):
    """Represents an active trading position."""
    
    id: Optional[str] = None
    asset_class: Union[AssetClass, str]
    symbol: str
    direction: Union[Direction, str]
    size: float
    entry_time: datetime
    entry_price: float
    current_price: float = 0.0
    unrealized_pnl_local: float = 0.0
    unrealized_pnl_base: float = 0.0
    
    class Config:
        use_enum_values = True
    
    def update_current_price(self, new_price: float, fx_rate: float = 1.0):
        """Update the current price and recalculate unrealized PnL."""
        self.current_price = new_price
        
        # Calculate PnL based on direction
        if self.direction == "buy":
            pnl_per_unit = new_price - self.entry_price
        else:  # SELL
            pnl_per_unit = self.entry_price - new_price
        
        self.unrealized_pnl_local = pnl_per_unit * self.size
        self.unrealized_pnl_base = self.unrealized_pnl_local * fx_rate
    
    def to_dict(self) -> dict:
        """Convert position to dictionary for API responses."""
        return {
            "id": self.id,
            "asset_class": self.asset_class,
            "symbol": self.symbol,
            "direction": self.direction,
            "size": self.size,
            "entry_time": self.entry_time.isoformat(),
            "entry_price": self.entry_price,
            "current_price": self.current_price,
            "unrealized_pnl_local": round(self.unrealized_pnl_local, 2),
            "unrealized_pnl_base": round(self.unrealized_pnl_base, 2)
        }