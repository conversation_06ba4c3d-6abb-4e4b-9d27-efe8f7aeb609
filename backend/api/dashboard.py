from fastapi import APIRouter, HTTPException
from services.database_service import DatabaseService
from services.fx_service import FxService

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])

# Initialize services
db_service = DatabaseService()
fx_service = FxService()

@router.get("/performance", response_model=dict)
async def get_performance_summary():
    """Get performance summary for dashboard KPIs."""
    try:
        summary = db_service.get_performance_summary()
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/currencies", response_model=dict)
async def get_supported_currencies():
    """Get supported currencies and FX pairs."""
    try:
        return {
            "base_currency": fx_service.base_currency,
            "supported_currencies": fx_service.get_supported_currencies(),
            "available_pairs": fx_service.get_available_pairs()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))