from fastapi import APIRouter, HTTPException
from datetime import datetime
from typing import List
from models.position import Position
from models.trade import Trade
from models.requests import CreatePositionRequest, ClosePositionRequest
from models.enums import AssetClass
from services.database_service import DatabaseService
from services.fx_service import FxService
from services.pricing_service import PricingService

router = APIRouter(prefix="/api/positions", tags=["positions"])

# Initialize services
db_service = DatabaseService()
fx_service = FxService()
pricing_service = PricingService()

@router.post("/", response_model=dict)
async def create_position(request: CreatePositionRequest):
    """Create a new trading position."""
    try:
        # Get current simulated price
        current_price = pricing_service.get_current_price(request.symbol)
        
        # Create position
        position = Position(
            asset_class=request.asset_class,
            symbol=request.symbol,
            direction=request.direction,
            size=request.size,
            entry_time=request.entry_time,
            entry_price=current_price,
            current_price=current_price
        )
        
        # Get FX conversion rate for PnL calculation
        # For FX pairs, extract base currency (first 3 chars)
        local_currency = request.symbol[:3] if request.asset_class == "fx" else "USD"
        fx_rate = fx_service.get_fx_conversion_rate(datetime.now(), local_currency)
        
        # Update PnL (initially zero since entry_price == current_price)
        position.update_current_price(current_price, fx_rate)
        
        # Save to database
        position_id = db_service.create_position(position)
        
        return {"message": "Position created successfully", "position_id": position_id}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[dict])
async def get_all_positions():
    """Get all open positions."""
    try:
        positions = db_service.get_all_positions()
        return [position.to_dict() for position in positions]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/close", response_model=dict)
async def close_position(request: ClosePositionRequest):
    """Close an existing position."""
    try:
        # Get position from database
        position = db_service.get_position_by_id(request.position_id)
        if not position:
            raise HTTPException(status_code=404, detail="Position not found")
        
        # Get current price for closing
        close_price = pricing_service.get_current_price(position.symbol)
        
        # Get FX conversion rate
        local_currency = position.symbol[:3] if position.asset_class == "fx" else "USD"
        fx_rate = fx_service.get_fx_conversion_rate(datetime.now(), local_currency)
        
        # Create trade record
        trade = Trade.from_position(position, datetime.now(), close_price, fx_rate)
        trade_id = db_service.create_trade(trade)
        
        # Remove position from database
        db_service.delete_position(request.position_id)
        
        return {
            "message": "Position closed successfully", 
            "trade_id": trade_id,
            "realized_pnl_base": trade.realized_pnl_base
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refresh-prices", response_model=dict)
async def refresh_prices():
    """Refresh prices for all open positions."""
    try:
        positions = db_service.get_all_positions()
        updated_count = 0
        
        # Update all prices in pricing service first
        pricing_service.update_prices()
        
        for position in positions:
            # Get updated price from pricing service
            new_price = pricing_service.get_current_price(position.symbol)
            
            # Get FX rate
            local_currency = position.symbol[:3] if position.asset_class == "fx" else "USD"
            fx_rate = fx_service.get_fx_conversion_rate(datetime.now(), local_currency)
            
            # Update position
            position.update_current_price(new_price, fx_rate)
            db_service.update_position(position)
            updated_count += 1
        
        return {"message": f"Refreshed prices for {updated_count} positions"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))