from fastapi import APIRouter, HTTPException
from typing import List
from services.database_service import DatabaseService

router = APIRouter(prefix="/api", tags=["equity-curve"])

# Initialize services
db_service = DatabaseService()

@router.get("/equity-curve-data", response_model=List[List])
async def get_equity_curve_data():
    """
    Get equity curve data for chart visualization.
    
    Returns data in format: [[date1, cumulative_pnl1], [date2, cumulative_pnl2], ...]
    where each date represents when one or more positions were closed,
    and cumulative_pnl is the running total of all realized PnL in base currency.
    """
    try:
        equity_data = db_service.get_equity_curve_data()
        return equity_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))