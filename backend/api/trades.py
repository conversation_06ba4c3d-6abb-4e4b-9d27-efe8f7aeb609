from fastapi import APIRouter, HTTPException
from typing import List
from services.database_service import DatabaseService

router = APIRouter(prefix="/api/trades", tags=["trades"])

# Initialize services
db_service = DatabaseService()

@router.get("/", response_model=List[dict])
async def get_all_trades():
    """Get all completed trades."""
    try:
        trades = db_service.get_all_trades()
        return [trade.to_dict() for trade in trades]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))