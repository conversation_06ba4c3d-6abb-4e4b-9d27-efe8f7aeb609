import datetime
import random
from typing import Dict, Any
import logging
from config.settings import settings

class FxService:
    """
    FX rate service for the trading simulator.
    Generates and caches simulated exchange rates for G8 currency pairs.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_currency = settings.base_currency
        
        # G8 currencies: USD, EUR, GBP, JPY, CHF, CAD, AUD, NZD
        self.g8_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD']
        
        # Major FX pairs with approximate realistic base rates
        self.base_rates = {
            'EURUSD': 1.0500, 'GBPUSD': 1.2700, 'USDJPY': 150.00, 'USDCHF': 0.9000,
            'USDCAD': 1.3500, 'AUDUSD': 0.6500, 'NZDUSD': 0.6000,
            'EURGBP': 0.8600, 'EURJPY': 157.50, 'EURCHF': 0.9450, 'EURCAD': 1.4175,
            'EURAUD': 1.6154, 'EURNZD': 1.7500, 'GBPJPY': 190.50, 'GBPCHF': 1.1430,
            'GBPCAD': 1.7145, 'GBPAUD': 1.9538, 'GBPNZD': 2.1167, 'CHFJPY': 166.67,
            'AUDCAD': 0.8775, 'AUDCHF': 0.5850, 'AUDJPY': 97.50, 'AUDNZD': 1.0833,
            'NZDCAD': 0.8100, 'NZDCHF': 0.5400, 'NZDJPY': 90.00, 'CADCHF': 0.6667,
            'CADJPY': 111.11
        }
        
        # Cache for daily rates - will be populated on startup and daily
        self.daily_rates_cache: Dict[str, Dict[datetime.date, float]] = {}
        
        # Initialize with current rates
        self._generate_daily_rates()
    
    def _generate_daily_rates(self):
        """Generate simulated FX rates for all pairs for the current date."""
        today = datetime.date.today()
        
        if not self.daily_rates_cache:
            # Initialize cache structure
            for pair in self.base_rates.keys():
                self.daily_rates_cache[pair] = {}
        
        # Generate rates for today if not already present
        for pair, base_rate in self.base_rates.items():
            if today not in self.daily_rates_cache[pair]:
                # Add some realistic volatility (±2% daily movement)
                volatility = random.uniform(-0.02, 0.02)
                simulated_rate = base_rate * (1 + volatility)
                self.daily_rates_cache[pair][today] = round(simulated_rate, 5)
        
        self.logger.info(f"Generated FX rates for {len(self.base_rates)} pairs on {today}")
    
    def get_fx_conversion_rate(self, dt: datetime.datetime, from_currency: str, to_currency: str = None) -> float:
        """
        Get the FX conversion rate from one currency to another.
        
        Args:
            dt: The datetime for the conversion
            from_currency: Source currency code
            to_currency: Target currency code (defaults to base_currency)
            
        Returns:
            Conversion rate as float
        """
        if to_currency is None:
            to_currency = self.base_currency
            
        if from_currency == to_currency:
            return 1.0
        
        # Try direct pair first
        from_to_pair = f"{from_currency}{to_currency}"
        if from_to_pair in self.base_rates:
            return self._find_fx_rate(from_to_pair, dt)
        
        # Try inverse pair
        to_from_pair = f"{to_currency}{from_currency}"
        if to_from_pair in self.base_rates:
            return 1.0 / self._find_fx_rate(to_from_pair, dt)
        
        # Cross currency calculation via USD
        if from_currency != 'USD' and to_currency != 'USD':
            from_usd_rate = self.get_fx_conversion_rate(dt, from_currency, 'USD')
            usd_to_rate = self.get_fx_conversion_rate(dt, 'USD', to_currency)
            return from_usd_rate * usd_to_rate
        
        raise ValueError(f"Cannot find conversion rate from {from_currency} to {to_currency}")
    
    def _find_fx_rate(self, pair: str, dt: datetime.datetime) -> float:
        """Find the FX rate for a given pair and datetime."""
        target_date = dt.date()
        
        # Check if we have the rate for the exact date
        if pair in self.daily_rates_cache and target_date in self.daily_rates_cache[pair]:
            return self.daily_rates_cache[pair][target_date]
        
        # Look back up to 10 days for the most recent rate
        current_date = target_date
        for i in range(10):
            if pair in self.daily_rates_cache and current_date in self.daily_rates_cache[pair]:
                return self.daily_rates_cache[pair][current_date]
            current_date -= datetime.timedelta(days=1)
        
        # If no historical rate found, generate one based on base rate
        base_rate = self.base_rates.get(pair)
        if base_rate:
            volatility = random.uniform(-0.01, 0.01)  # Small volatility for historical rates
            simulated_rate = base_rate * (1 + volatility)
            
            # Cache this rate
            if pair not in self.daily_rates_cache:
                self.daily_rates_cache[pair] = {}
            self.daily_rates_cache[pair][target_date] = round(simulated_rate, 5)
            
            return self.daily_rates_cache[pair][target_date]
        
        raise ValueError(f"Could not find FX rate for pair: {pair} on date: {target_date}")
    
    def get_supported_currencies(self) -> list[str]:
        """Return list of supported G8 currencies."""
        return self.g8_currencies.copy()
    
    def get_available_pairs(self) -> list[str]:
        """Return list of available currency pairs."""
        return list(self.base_rates.keys())
    
    def refresh_daily_rates(self):
        """Refresh the daily FX rates - called daily or on demand."""
        self._generate_daily_rates()
        self.logger.info("FX rates refreshed")