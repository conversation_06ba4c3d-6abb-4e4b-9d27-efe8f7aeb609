import random
import time
from datetime import datetime, timedelta
from typing import Dict, List
import asyncio
import logging

class PricingService:
    """Simulated pricing service for generating realistic market prices."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Base prices with realistic values
        self.base_prices = {
            "EURUSD": 1.0500,
            "GBPUSD": 1.2700,
            "USDJPY": 150.00,
            "USDCHF": 0.9000,
            "USDCAD": 1.3500,
            "AUDUSD": 0.6500,
            "NZDUSD": 0.6000,
            "EURGBP": 0.8600,
            "EURJPY": 157.50,
            "EURCHF": 0.9450,
        }
        
        # Current prices (will be updated with simulation)
        self.current_prices = self.base_prices.copy()
        
        # Price volatility parameters (daily volatility as percentage)
        self.volatility = {
            "EURUSD": 0.008,  # 0.8%
            "GBPUSD": 0.012,  # 1.2%
            "USDJPY": 0.010,  # 1.0%
            "USDCHF": 0.008,  # 0.8%
            "USDCAD": 0.009,  # 0.9%
            "AUDUSD": 0.015,  # 1.5%
            "NZDUSD": 0.015,  # 1.5%
            "EURGBP": 0.007,  # 0.7%
            "EURJPY": 0.012,  # 1.2%
            "EURCHF": 0.006,  # 0.6%
        }
        
        # Trend bias (for more realistic price movements)
        self.trend_bias = {symbol: 0.0 for symbol in self.base_prices.keys()}
        self.trend_update_time = datetime.now()
        
    def get_current_price(self, symbol: str) -> float:
        """Get current simulated price for a symbol."""
        if symbol not in self.current_prices:
            # Return a default price if symbol not found
            return 1.0
        
        return round(self.current_prices[symbol], 5)
    
    def get_all_current_prices(self) -> Dict[str, float]:
        """Get all current prices."""
        return {symbol: round(price, 5) for symbol, price in self.current_prices.items()}
    
    def update_prices(self):
        """Update all prices with simulated market movements."""
        current_time = datetime.now()
        
        # Update trend bias occasionally (every few minutes)
        if (current_time - self.trend_update_time).total_seconds() > 180:  # 3 minutes
            self._update_trend_bias()
            self.trend_update_time = current_time
        
        for symbol in self.current_prices.keys():
            # Generate random price movement
            volatility = self.volatility.get(symbol, 0.01)
            
            # Base random movement (Gaussian distribution)
            base_movement = random.gauss(0, volatility / 100)  # Convert percentage to decimal
            
            # Add trend bias
            trend = self.trend_bias.get(symbol, 0)
            total_movement = base_movement + (trend * volatility / 100)
            
            # Apply price movement
            old_price = self.current_prices[symbol]
            new_price = old_price * (1 + total_movement)
            
            # Ensure price doesn't deviate too much from base price (risk management)
            base_price = self.base_prices[symbol]
            max_deviation = 0.1  # 10% max deviation from base
            
            if abs(new_price - base_price) / base_price > max_deviation:
                # Pull back towards base price
                new_price = base_price + (new_price - base_price) * 0.5
            
            self.current_prices[symbol] = new_price
        
        self.logger.debug(f"Updated prices for {len(self.current_prices)} symbols")
    
    def _update_trend_bias(self):
        """Update trend bias for more realistic market behavior."""
        for symbol in self.trend_bias.keys():
            # Random walk for trend bias
            change = random.gauss(0, 0.1)  # Small changes in bias
            self.trend_bias[symbol] = max(-0.5, min(0.5, self.trend_bias[symbol] + change))
    
    def add_symbol(self, symbol: str, base_price: float, volatility: float = 0.01):
        """Add a new symbol to the pricing service."""
        self.base_prices[symbol] = base_price
        self.current_prices[symbol] = base_price
        self.volatility[symbol] = volatility
        self.trend_bias[symbol] = 0.0
        
        self.logger.info(f"Added new symbol: {symbol} with base price: {base_price}")
    
    def get_historical_prices(self, symbol: str, days: int = 30) -> List[Dict]:
        """Generate simulated historical prices for backtesting/analysis."""
        if symbol not in self.base_prices:
            return []
        
        base_price = self.base_prices[symbol]
        volatility = self.volatility.get(symbol, 0.01)
        
        historical_data = []
        current_price = base_price
        start_date = datetime.now() - timedelta(days=days)
        
        for i in range(days):
            date = start_date + timedelta(days=i)
            
            # Generate daily movement
            daily_movement = random.gauss(0, volatility)
            current_price = current_price * (1 + daily_movement)
            
            # Ensure price doesn't deviate too much
            if abs(current_price - base_price) / base_price > 0.15:  # 15% max deviation
                current_price = base_price + (current_price - base_price) * 0.7
            
            historical_data.append({
                "date": date.date().isoformat(),
                "price": round(current_price, 5),
                "symbol": symbol
            })
        
        return historical_data