import duckdb
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
from config.settings import settings
from models.position import Position
from models.trade import Trade
from models.enums import Direction, AssetClass

class DatabaseService:
    """Database service for managing positions and trades using DuckDB."""
    
    def __init__(self):
        self.db_path = settings.database_path
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables."""
        conn = duckdb.connect(self.db_path)
        
        # Create positions table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS positions (
                id VARCHAR PRIMARY KEY,
                asset_class VARCHAR NOT NULL,
                symbol VARCHAR NOT NULL,
                direction VARCHAR NOT NULL,
                size DOUBLE NOT NULL,
                entry_time TIMESTAMP NOT NULL,
                entry_price DOUBLE NOT NULL,
                current_price DOUBLE DEFAULT 0.0,
                unrealized_pnl_local DOUBLE DEFAULT 0.0,
                unrealized_pnl_base DOUBLE DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create trades table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id VARCHAR PRIMARY KEY,
                asset_class VARCHAR NOT NULL,
                symbol VARCHAR NOT NULL,
                direction VARCHAR NOT NULL,
                size DOUBLE NOT NULL,
                entry_time TIMESTAMP NOT NULL,
                entry_price DOUBLE NOT NULL,
                close_time TIMESTAMP NOT NULL,
                close_price DOUBLE NOT NULL,
                realized_pnl_local DOUBLE NOT NULL,
                realized_pnl_base DOUBLE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.close()
    
    def create_position(self, position: Position) -> str:
        """Create a new position and return its ID."""
        position.id = str(uuid.uuid4())
        
        conn = duckdb.connect(self.db_path)
        # Handle both string and enum values
        asset_class_val = position.asset_class.value if hasattr(position.asset_class, 'value') else position.asset_class
        direction_val = position.direction.value if hasattr(position.direction, 'value') else position.direction
        
        conn.execute("""
            INSERT INTO positions (
                id, asset_class, symbol, direction, size, entry_time, entry_price,
                current_price, unrealized_pnl_local, unrealized_pnl_base
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, [
            position.id, asset_class_val, position.symbol, 
            direction_val, position.size, position.entry_time,
            position.entry_price, position.current_price, 
            position.unrealized_pnl_local, position.unrealized_pnl_base
        ])
        conn.close()
        
        return position.id
    
    def get_all_positions(self) -> List[Position]:
        """Get all open positions."""
        conn = duckdb.connect(self.db_path)
        result = conn.execute("SELECT * FROM positions ORDER BY entry_time DESC").fetchall()
        conn.close()
        
        positions = []
        for row in result:
            positions.append(Position(
                id=row[0],
                asset_class=row[1],  # Keep as string, Position model handles it
                symbol=row[2],
                direction=row[3],    # Keep as string, Position model handles it
                size=row[4],
                entry_time=row[5],
                entry_price=row[6],
                current_price=row[7],
                unrealized_pnl_local=row[8],
                unrealized_pnl_base=row[9]
            ))
        
        return positions
    
    def get_position_by_id(self, position_id: str) -> Optional[Position]:
        """Get a position by its ID."""
        conn = duckdb.connect(self.db_path)
        result = conn.execute("SELECT * FROM positions WHERE id = ?", [position_id]).fetchone()
        conn.close()
        
        if not result:
            return None
        
        return Position(
            id=result[0],
            asset_class=result[1],
            symbol=result[2],
            direction=result[3],
            size=result[4],
            entry_time=result[5],
            entry_price=result[6],
            current_price=result[7],
            unrealized_pnl_local=result[8],
            unrealized_pnl_base=result[9]
        )
    
    def update_position(self, position: Position):
        """Update an existing position."""
        conn = duckdb.connect(self.db_path)
        conn.execute("""
            UPDATE positions SET 
                current_price = ?, unrealized_pnl_local = ?, unrealized_pnl_base = ?
            WHERE id = ?
        """, [position.current_price, position.unrealized_pnl_local, 
              position.unrealized_pnl_base, position.id])
        conn.close()
    
    def delete_position(self, position_id: str):
        """Delete a position."""
        conn = duckdb.connect(self.db_path)
        conn.execute("DELETE FROM positions WHERE id = ?", [position_id])
        conn.close()
    
    def create_trade(self, trade: Trade) -> str:
        """Create a new trade and return its ID."""
        trade.id = str(uuid.uuid4())
        
        conn = duckdb.connect(self.db_path)
        # Handle both string and enum values
        asset_class_val = trade.asset_class.value if hasattr(trade.asset_class, 'value') else trade.asset_class
        direction_val = trade.direction.value if hasattr(trade.direction, 'value') else trade.direction
        
        conn.execute("""
            INSERT INTO trades (
                id, asset_class, symbol, direction, size, entry_time, entry_price,
                close_time, close_price, realized_pnl_local, realized_pnl_base
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, [
            trade.id, asset_class_val, trade.symbol, direction_val,
            trade.size, trade.entry_time, trade.entry_price, trade.close_time,
            trade.close_price, trade.realized_pnl_local, trade.realized_pnl_base
        ])
        conn.close()
        
        return trade.id
    
    def get_all_trades(self) -> List[Trade]:
        """Get all completed trades."""
        conn = duckdb.connect(self.db_path)
        result = conn.execute("SELECT * FROM trades ORDER BY close_time DESC").fetchall()
        conn.close()
        
        trades = []
        for row in result:
            trades.append(Trade(
                id=row[0],
                asset_class=row[1],
                symbol=row[2],
                direction=row[3],
                size=row[4],
                entry_time=row[5],
                entry_price=row[6],
                close_time=row[7],
                close_price=row[8],
                realized_pnl_local=row[9],
                realized_pnl_base=row[10]
            ))
        
        return trades
    
    def get_equity_curve_data(self) -> List[List]:
        """Get equity curve data aggregated by date."""
        conn = duckdb.connect(self.db_path)
        
        # Aggregate realized PnL by closing date and calculate cumulative sum
        result = conn.execute("""
            SELECT 
                DATE(close_time) as trade_date,
                SUM(realized_pnl_base) as daily_pnl,
                SUM(SUM(realized_pnl_base)) OVER (ORDER BY DATE(close_time)) as cumulative_pnl
            FROM trades 
            GROUP BY DATE(close_time)
            ORDER BY trade_date
        """).fetchall()
        
        conn.close()
        
        # Convert to format expected by frontend: [[date, cumulative_pnl], ...]
        equity_data = []
        for row in result:
            date_str = row[0].isoformat() if row[0] else ""
            cumulative_pnl = float(row[2]) if row[2] else 0.0
            equity_data.append([date_str, cumulative_pnl])
        
        return equity_data
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for dashboard."""
        conn = duckdb.connect(self.db_path)
        
        # Get total realized PnL
        realized_result = conn.execute("SELECT COALESCE(SUM(realized_pnl_base), 0) FROM trades").fetchone()
        total_realized_pnl = float(realized_result[0]) if realized_result else 0.0
        
        # Get total unrealized PnL
        unrealized_result = conn.execute("SELECT COALESCE(SUM(unrealized_pnl_base), 0) FROM positions").fetchone()
        total_unrealized_pnl = float(unrealized_result[0]) if unrealized_result else 0.0
        
        conn.close()
        
        return {
            "total_realized_pnl": round(total_realized_pnl, 2),
            "total_unrealized_pnl": round(total_unrealized_pnl, 2),
            "total_pnl": round(total_realized_pnl + total_unrealized_pnl, 2)
        }