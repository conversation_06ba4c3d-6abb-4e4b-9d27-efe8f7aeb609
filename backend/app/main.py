import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from config.settings import settings
from api import positions, trades, dashboard, equity_curve
from services.fx_service import FxService

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print(f"Starting Trading Simulator on {settings.host_url}:{settings.port}")
    print(f"Base Currency: {settings.base_currency}")
    
    # Initialize FX service
    fx_service = FxService()
    fx_service.refresh_daily_rates()
    print("FX rates initialized")
    
    yield
    # Shutdown
    print("Shutting down Trading Simulator")

app = FastAPI(
    title="Trading Simulator API",
    description="A simulated trading application for FX instruments",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Added Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(positions.router)
app.include_router(trades.router)
app.include_router(dashboard.router)
app.include_router(equity_curve.router)

@app.get("/")
async def root():
    return {"message": "Trading Simulator API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "base_currency": settings.base_currency}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host=settings.host_url, port=settings.port, reload=settings.debug)