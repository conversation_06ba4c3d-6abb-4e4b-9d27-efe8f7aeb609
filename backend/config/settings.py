import configparser
import os
from pathlib import Path

class Settings:
    def __init__(self):
        self.config = configparser.ConfigParser()
        config_path = Path(__file__).parent.parent / "config.ini"
        self.config.read(config_path)
        
    @property
    def host_url(self) -> str:
        return self.config.get('DEFAULT', 'HOST_URL', fallback='localhost')
    
    @property
    def port(self) -> int:
        return self.config.getint('DEFAULT', 'PORT', fallback=8000)
    
    @property
    def base_currency(self) -> str:
        return self.config.get('DEFAULT', 'BASE_CURRENCY', fallback='USD')
    
    @property
    def database_path(self) -> str:
        return self.config.get('DEFAULT', 'DATABASE_PATH', fallback='./data/trading_simulator.db')
    
    @property
    def price_update_interval(self) -> int:
        return self.config.getint('DEFAULT', 'PRICE_UPDATE_INTERVAL_SECONDS', fallback=5)
    
    @property
    def debug(self) -> bool:
        return self.config.getboolean('DEFAULT', 'DEBUG', fallback=False)

settings = Settings()